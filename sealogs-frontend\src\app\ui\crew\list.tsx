'use client'

import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, DialogTrigger } from 'react-aria-components'
import { CREW_BRIEF_LIST, ReadDepartments } from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import Link from 'next/link'
import { UPDATE_USER } from '@/app/lib/graphQL/mutation'
import {
    getCrewDuties,
    GetCrewListWithTrainingStatus,
    getVesselBriefList,
} from '@/app/lib/actions'

import { isEmpty } from 'lodash'
import { useRouter } from 'next/navigation'
import { useSidebar } from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import {
    Avatar,
    AvatarFallback,
    Badge,
    Popover as CustomPopover,
    get<PERSON><PERSON><PERSON><PERSON>tial<PERSON>,
    H1,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    PopoverTrigger,
} from '@/components/ui'
import { CircleAlert } from 'lucide-react'
import { SealogsCrewIcon } from '@/app/lib/icons/SealogsCrewIcon'
import { VesselsFilterActions } from '@/components/filter/components/vessels-actions'
import { CrewFilterActions } from '@/components/filter/components/crew-actions'
import { ListHeader } from '@/components/ui/list-header'
import VesselIcon from '../vessels/vesel-icon'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'

export default function CrewList(props: any) {
    const router = useRouter()
    const [crewList, setCrewList] = useState<any>([])
    const [vessels, setVessels] = useState<
        Array<{ label: string; value: number }>
    >([])
    const { vesselIconData, getVesselWithIcon } = useVesselIconData()
    const [showActiveUsers, setShowActiveUsers] = useState(true)
    const { isMobile } = useSidebar()
    const [isLoading, setIsLoading] = useState(true)
    const [departments, setDepartments] = useState<any>([])
    const [duties, setDuties] = useState<any>([])
    const limit = 100
    const [pageInfo, setPageInfo] = useState({
        totalCount: 0,
        hasNextPage: false,
        hasPreviousPage: false,
    })
    const [page, setPage] = useState(0)
    let [filter, setFilter] = useState({
        isArchived: { eq: false },
    } as SearchFilter)
    const [trainingStatusFilter, setTrainingStatusFilter] = useState<
        string | null
    >(null)

    // Update crew duties based on active/archived state.
    const handleSetCrewDuties = (crewDuties: any) => {
        const activeDuties = crewDuties.filter((duty: any) =>
            showActiveUsers ? !duty.archived : duty.archived,
        )
        const formattedCrewDuties = activeDuties.map((duty: any) => {
            return {
                label: duty.title,
                value: duty.id,
            }
        })
        setDuties(formattedCrewDuties)
    }
    getCrewDuties(handleSetCrewDuties)

    // Render departments recursively.
    const renderDepartment = (
        departments: any[],
        parentID: number = 0,
        depth: number = 0,
    ): any[] => {
        return departments
            .filter((department: any) => +department.parentID === parentID)
            .flatMap((department: any) => {
                const children = renderDepartment(
                    departments,
                    +department.id,
                    depth + 1,
                )
                const item = {
                    ...department,
                    level: depth,
                }
                return [item, ...children]
            })
    }
    const [readDepartments, { loading: readDepartmentsLoading }] = useLazyQuery(
        ReadDepartments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readDepartments.nodes
                if (data) {
                    const formattedData = renderDepartment(data)
                    setDepartments(formattedData)
                }
            },
            onError: (error: any) => {
                console.error('queryCrewMembers error', error)
            },
        },
    )
    const loadDepartments = async () => {
        await readDepartments()
    }
    useEffect(() => {
        if (isLoading) {
            loadDepartments()
            setIsLoading(false)
        }
    }, [isLoading])

    // Set vessels from vessel brief list.
    const handleSetVessels = (vessels: any) => {
        const vesselSelection = vessels.map((vessel: any) => {
            return { label: vessel.title, value: vessel.id }
        })
        setVessels(vesselSelection)
        //loadCrewMembers()
    }
    getVesselBriefList(handleSetVessels)

    const [queryCrewMembers] = useLazyQuery(CREW_BRIEF_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            handleSetCrewMembers(response.readSeaLogsMembers.nodes)
            setPageInfo(response.readSeaLogsMembers.pageInfo)
            return response.readSeaLogsMembers.nodes
        },
        onError: (error: any) => {
            console.error('queryCrewMembers error', error)
        },
    })
    const handleSetCrewMembers = (crewMembers: any) => {
        const transformedCrewList = GetCrewListWithTrainingStatus(
            crewMembers,
            vessels,
        )
        setCrewList(transformedCrewList)
    }

    // Function to filter crew list by training status
    const filterCrewByTrainingStatus = (
        crewList: any[],
        statusFilter: string | null,
    ) => {
        if (!statusFilter) return crewList

        return crewList.filter((crew: any) => {
            const trainingStatus = crew.trainingStatus?.label
            const dues = crew.trainingStatus?.dues || []

            if (statusFilter === 'Good') {
                return trainingStatus === 'Good'
            } else if (statusFilter === 'Overdue') {
                return trainingStatus === 'Overdue'
            } else if (statusFilter === 'Due Soon') {
                // Due Soon is represented by an empty string label with dues
                // This happens when there are training sessions due within 7 days but not overdue
                return trainingStatus === ' ' && dues.length > 0
            }

            return true
        })
    }

    // Get filtered crew list for display
    const filteredCrewList = filterCrewByTrainingStatus(
        crewList,
        trainingStatusFilter,
    )

    const loadCrewMembers = async (
        startPage: number = 0,
        searchFilter: any = { ...filter },
    ) => {
        /*searchFilter.isArchived = { eq: !showActiveUsers }
        const updatedFilter: SearchFilter = {
            ...searchFilter,
            isArchived: { eq: !showActiveUsers },
        }*/
        await queryCrewMembers({
            variables: {
                limit: limit,
                offset: startPage * limit,
                filter: searchFilter,
            },
        })
    }

    const [mutationUpdateUser] = useMutation(UPDATE_USER, {
        onCompleted: () => {},
        onError: (error: any) => {
            console.error('mutationUpdateUser error', error)
        },
    })

    const handleCrewDuty = async (duty: any, user: any) => {
        const selectedUser = {
            ...crewList.find((crew: any) => crew.ID === user.ID),
        }
        const newPrimaryDutyID = duty.value
        if (selectedUser) {
            const updatedCrewList = crewList.map((crew: any) => {
                if (crew.ID === user.ID) {
                    return {
                        ...crew,
                        PrimaryDutyID: newPrimaryDutyID,
                    }
                }
                return crew
            })
            setCrewList(updatedCrewList)
            // Update user
            const variables = {
                input: {
                    id: +user.id,
                    primaryDutyID: newPrimaryDutyID,
                },
            }
            await mutationUpdateUser({ variables })
        }
    }

    const handleNavigationClick = (newPage: any) => {
        if (newPage < 0 || newPage === page) return
        setPage(newPage)
        loadCrewMembers(newPage)
    }

    const handleFilterOnChange = ({ type, data }: any) => {
        interface SearchFilter {
            isArchived?: { eq: boolean }
            vehicles?: { id: { contains?: number; in?: number[] } }
            primaryDutyID?: { eq?: number; in?: number[] }
            q?: { contains: string }
        }

        const searchFilter: SearchFilter = { ...filter }

        // Handle training status filter separately since it's client-side
        if (type === 'trainingStatus') {
            if (data && data.value) {
                setTrainingStatusFilter(data.value)
            } else {
                setTrainingStatusFilter(null)
            }
            return // Don't reload crew members for client-side filter
        }

        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.vehicles = {
                    id: { in: data.map((item) => +item.value) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.vehicles = { id: { contains: +data.value } }
            } else {
                delete searchFilter.vehicles
            }
        }
        if (type === 'crewDuty') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.primaryDutyID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.primaryDutyID = { eq: +data.value }
            } else {
                delete searchFilter.primaryDutyID
            }
        }
        if (type === 'keyword') {
            if (!isEmpty(data.value)) {
                searchFilter.q = { contains: data.value }
            } else {
                delete searchFilter.q
            }
        }
        if (type === 'isArchived') {
            if (data !== undefined) {
                searchFilter.isArchived = { eq: !data }
            } else {
                delete searchFilter.isArchived
            }
        }
        setFilter(searchFilter)
        //setPage(0)
        loadCrewMembers(0, searchFilter)
    }

    useEffect(() => {
        setPage(0)
        loadCrewMembers(0, filter)
    }, [showActiveUsers, filter])

    // Column definitions for the DataTable.
    const columns: ExtendedColumnDef<any, unknown>[] = [
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Name" />
            ),
            cell: ({ row }: { row: any }) => {
                const crewMember: any = row.original
                const fullName = `${crewMember.firstName} ${crewMember.surname}`
                return (
                    <div className="flex-1 flex justify-start items-center gap-2">
                        <Avatar
                            size="sm"
                            variant={
                                crewMember.trainingStatus?.label === 'Overdue'
                                    ? 'destructive'
                                    : 'success'
                            }>
                            <AvatarFallback>
                                {getCrewInitials(
                                    crewMember.firstName,
                                    crewMember.surname,
                                )}
                            </AvatarFallback>
                        </Avatar>
                        <div className="grid min-w-32">
                            <Link
                                href={`/crew/info?id=${crewMember.id}`}
                                className="items-center truncate pl-2 text-nowrap">
                                {fullName || '--'}
                            </Link>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const fullNameA =
                    `${rowA?.original?.firstName} ${rowA?.original?.surname}` ||
                    ''
                const fullNameB =
                    `${rowB?.original?.firstName} ${rowB?.original?.surname}` ||
                    ''
                return fullNameA.localeCompare(fullNameB)
            },
        },
        {
            accessorKey: 'vehicles',
            cellAlignment: 'left',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            cell: ({ row }: { row: any }) => {
                const crew = row.original

                return (
                    <div className="flex flex-col gap-2 py-2.5">
                        {crew.vehicles.nodes.map((vessel: any) => {
                            // Get complete vessel data with icon information
                            const vesselWithIcon = getVesselWithIcon(
                                vessel.id,
                                vessel,
                            )
                            return (
                                <div
                                    key={String(vessel.id)}
                                    className="flex items-center text-start gap-2">
                                    <div className="min-w-fit">
                                        <VesselIcon vessel={vesselWithIcon} />
                                    </div>
                                    <P className="hidden sm:block">
                                        {vessel.title}
                                    </P>
                                </div>
                            )
                        })}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const titleA = rowA?.original?.vehicles?.nodes?.[0]?.title || ''
                const titleB = rowB?.original?.vehicles?.nodes?.[0]?.title || ''

                return titleA.localeCompare(titleB)
            },
        },
        {
            accessorKey: 'primaryDuty',
            cellAlignment: 'right',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Primary Duty" />
            ),
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <div className="whitespace-normal px-5">
                        {crew.primaryDuty.title}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.primaryDuty?.title || ''
                const valueB = rowB?.original?.primaryDuty?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'trainingStatus',
            header: ({ column }: { column: any }) => (
                <div className="flex justify-center w-full">
                    <DataTableSortHeader column={column} title="Training" />
                </div>
            ),
            cell: ({ row }: { row: any }) => {
                const crew = row.original

                return (
                    <div className="flex justify-center w-full">
                        {crew.trainingStatus.label === 'Overdue' ? (
                            <Badge variant="destructive">
                                {crew.trainingStatus.dues.length}
                            </Badge>
                        ) : (
                            <Badge variant="success">
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </Badge>
                        )}
                    </div>
                )
            },
        },
    ]

    const handleDropdownChange = (type: string, data: any) => {
        handleFilterOnChange({ type, data })
    }

    return (
        <>
            <ListHeader
                icon={
                    <SealogsCrewIcon
                        className={`h-12 w-12 ring-1 p-1 rounded-full`}
                    />
                }
                title="All crew"
                actions={
                    <CrewFilterActions
                        onChange={(data: any) => {
                            handleDropdownChange('isArchived', data)
                        }}
                    />
                }
            />
            <div className="mt-16">
                <DataTable
                    columns={columns}
                    data={filteredCrewList}
                    pageSize={20}
                    onChange={handleFilterOnChange}
                />
            </div>
        </>
    )
}

export const CrewTable = ({
    crewList,
    vessels,
    handleCrewDuty = false,
    showSurname,
}: any) => {
    const [isAdmin, setIsAdmin] = useState(false)
    const [departments, setDepartments] = useState([] as any)
    const [isLoading, setIsLoading] = useState(true)
    const renderDepartment = (
        departments: any[],
        parentID: number = 0,
        depth: number = 0,
    ): any[] => {
        return departments
            .filter((department: any) => +department.parentID === parentID)
            .flatMap((department: any) => {
                const children = renderDepartment(
                    departments,
                    +department.id,
                    depth + 1,
                )
                const item = {
                    ...department,
                    level: depth,
                }
                return [item, ...children]
            })
    }
    const [readDepartments, { loading: readDepartmentsLoading }] = useLazyQuery(
        ReadDepartments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readDepartments.nodes
                if (data) {
                    const formattedData = renderDepartment(data)
                    setDepartments(formattedData)
                }
            },
            onError: (error: any) => {
                console.error('queryCrewMembers error', error)
            },
        },
    )
    const loadDepartments = async () => {
        await readDepartments()
    }
    useEffect(() => {
        if (isLoading) {
            loadDepartments()
            setIsLoading(false)
        }
    }, [isLoading])
    const crewListWithTrainingStatus = GetCrewListWithTrainingStatus(
        crewList,
        vessels,
    )
    const transformedCrewList = crewListWithTrainingStatus.map(
        (crewMember: any) => {
            const filteredDues = crewMember.trainingStatus.dues.filter(
                (due: any) => {
                    return crewMember.vehicles.nodes.some(
                        (node: any) => node.id === due.vesselID,
                    )
                },
            )
            const updatedTrainingStatus = {
                ...crewMember.trainingStatus,
                dues: filteredDues,
            }
            if (filteredDues.length === 0) {
                updatedTrainingStatus.label = 'Good'
            }
            return { ...crewMember, trainingStatus: updatedTrainingStatus }
        },
    )

    useEffect(() => {
        if (
            typeof window !== 'undefined' &&
            typeof window.localStorage !== 'undefined'
        ) {
            const result = localStorage.getItem('admin')
            const admin = result === 'true'
            setIsAdmin(admin)
        }
    }, [])

    const columns = [
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const crewMember: any = row.original
                return (
                    <div className="p-2 text-left align-middle items-center border-y md:border-0 border-slblue-100">
                        <div>
                            <Avatar
                                size="sm"
                                variant={
                                    crewMember.trainingStatus?.label !== 'Good'
                                        ? 'destructive'
                                        : 'success'
                                }>
                                <AvatarFallback>
                                    {getCrewInitials(
                                        crewMember.firstName,
                                        crewMember.surname,
                                    )}
                                </AvatarFallback>
                            </Avatar>
                            <Link
                                href={`/crew/info?id=${crewMember.id}`}
                                className="flex items-center pl-2 text-nowrap">
                                {crewMember.firstName || '--'}
                                {showSurname == true ? (
                                    <span>
                                        &nbsp;
                                        {crewMember.surname || '--'}
                                    </span>
                                ) : (
                                    <span className="hidden md:flex">
                                        &nbsp;
                                        {crewMember.surname || '--'}
                                    </span>
                                )}
                            </Link>
                        </div>

                        {handleCrewDuty && (
                            <div className="flex md:hidden flex-col">
                                {crewMember.vehicles.nodes &&
                                    crewMember.vehicles.nodes.map(
                                        (vessel: any, index: number) => {
                                            if (index < 2) {
                                                return (
                                                    <div
                                                        key={vessel.id}
                                                        className="bg-slblue-50 font-light rounded-lg p-2 border m-1 border-slblue-200 text-nowrap">
                                                        <a
                                                            className="max-w-32 overflow-hidden block"
                                                            href={`/vessel/info?id=${vessel.id}`}>
                                                            {vessel.title}
                                                        </a>
                                                    </div>
                                                )
                                            }
                                            if (index === 2) {
                                                return (
                                                    <DialogTrigger
                                                        key={vessel.id}>
                                                        <Button
                                                            style={{
                                                                color: 'orange',
                                                            }}
                                                            className="inline-block bg-slblue-50 border border-slblue-200 font-light rounded-lg text-sm mr-1 p-2 outline-none">
                                                            +{' '}
                                                            {crewMember.vehicles
                                                                .nodes.length -
                                                                2}{' '}
                                                            more
                                                        </Button>
                                                        <Popover>
                                                            <div className="p-0 max-h-full bg-slblue-100 rounded ">
                                                                {crewMember.vehicles.nodes
                                                                    .slice(2)
                                                                    .map(
                                                                        (
                                                                            v: any,
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    v.id
                                                                                }
                                                                                className="flex cursor-pointer hover:bg-sllightblue-1000 items-center overflow-auto ps-3 py-2">
                                                                                <div className="text-sm">
                                                                                    <a
                                                                                        href={`/vessel/info?id=${v.id}`}>
                                                                                        {
                                                                                            v.title
                                                                                        }
                                                                                    </a>
                                                                                </div>
                                                                            </div>
                                                                        ),
                                                                    )}
                                                            </div>
                                                        </Popover>
                                                    </DialogTrigger>
                                                )
                                            }
                                            return null
                                        },
                                    )}
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'vessel',
            header: () => <>{handleCrewDuty && 'Vessel'}</>,
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <>
                        {handleCrewDuty && (
                            <div className="hidden md:table-cell align-middle text-right p-2">
                                {crew.vehicles.nodes &&
                                    crew.vehicles.nodes.map(
                                        (vessel: any, index: number) => {
                                            if (index < 2) {
                                                return (
                                                    <div
                                                        key={vessel.id}
                                                        className="bg-slblue-50 inline-block font-light rounded-lg p-2 border border-slblue-200 m-1">
                                                        <a
                                                            href={`/vessel/info?id=${vessel.id}`}>
                                                            {vessel.title}
                                                        </a>
                                                    </div>
                                                )
                                            }
                                            if (index === 2) {
                                                return (
                                                    <DialogTrigger
                                                        key={vessel.id}>
                                                        <Button
                                                            style={{
                                                                color: 'orange',
                                                            }}
                                                            className="inline-block bg-slblue-50 border border-slblue-200 font-light rounded-lg text-sm mr-1 p-2 outline-none">
                                                            +{' '}
                                                            {crew.vehicles.nodes
                                                                .length -
                                                                2}{' '}
                                                            more
                                                        </Button>
                                                        <Popover>
                                                            <div className="p-0 w-64 max-h-full bg-slblue-100 rounded ">
                                                                {crew.vehicles.nodes
                                                                    .slice(2)
                                                                    .map(
                                                                        (
                                                                            v: any,
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    v.id
                                                                                }
                                                                                className="flex cursor-pointer hover:bg-sllightblue-1000 items-center overflow-auto ps-3 py-2">
                                                                                <div className="text-sm">
                                                                                    <a
                                                                                        href={`/vessel/info?id=${v.id}`}>
                                                                                        {
                                                                                            v.title
                                                                                        }
                                                                                    </a>
                                                                                </div>
                                                                            </div>
                                                                        ),
                                                                    )}
                                                            </div>
                                                        </Popover>
                                                    </DialogTrigger>
                                                )
                                            }
                                            return null
                                        },
                                    )}
                            </div>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'primaryDuty',
            header: 'Primary Duty',
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <div className="text-wrap text-right whitespace-normal">
                        {crew.primaryDuty.title}
                    </div>
                )
            },
        },
        {
            accessorKey: 'trainingStatus',
            header: 'Training Status',
            cell: ({ row }: { row: any }) => {
                const crew = row.original

                return (
                    <div className="p-2 border-y md:border-0 border-slblue-100">
                        <div className="flex justify-center">
                            {crew.trainingStatus.label !== 'Good' ? (
                                <div>
                                    <CustomPopover triggerType="hover">
                                        <PopoverTrigger asChild>
                                            <CircleAlert
                                                strokeWidth={1}
                                                className="h-9 w-9 text-destructive"
                                            />
                                        </PopoverTrigger>
                                        <PopoverContent>
                                            <div className="bg-slblue-100 rounded p-2">
                                                <div className="text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded">
                                                    {crew.trainingStatus.dues.map(
                                                        (
                                                            item: any,
                                                            dueIndex: number,
                                                        ) => (
                                                            <div key={dueIndex}>
                                                                {`${item.trainingType.title} - ${item.status.label}`}
                                                            </div>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        </PopoverContent>
                                    </CustomPopover>
                                </div>
                            ) : (
                                <div
                                    className={`text-accent border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8`}>
                                    <svg
                                        className={`h-5 w-5`}
                                        viewBox="0 0 20 20"
                                        fill="#27AB83"
                                        aria-hidden="true">
                                        <path
                                            fillRule="evenodd"
                                            d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                </div>
                            )}
                        </div>
                    </div>
                )
            },
        },
        {
            accessorKey: 'departments',
            header: () => (
                <>
                    {isAdmin &&
                        localStorage.getItem('useDepartment') === 'true' &&
                        'Departments'}
                </>
            ),
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <div>
                        {isAdmin &&
                            localStorage.getItem('useDepartment') ===
                                'true' && (
                                <div className="p-2 text-left align-middle items-center border-y md:border-0 border-slblue-100 hidden md:table-cell  text-xs">
                                    {crew.departments &&
                                    crew.departments.nodes.length > 0 ? (
                                        crew.departments.nodes.map(
                                            (department: any) => (
                                                <Link
                                                    key={department.id}
                                                    href={`/department/info?id=${department.id}`}
                                                    className="flex flex-col text-nowrap">
                                                    {
                                                        departments.find(
                                                            (dept: any) =>
                                                                dept.id ===
                                                                department.id,
                                                        )?.title
                                                    }
                                                </Link>
                                            ),
                                        )
                                    ) : (
                                        <span>No departments found</span>
                                    )}
                                </div>
                            )}
                    </div>
                )
            },
        },
    ]

    return (
        <div className="relative w-full rounded-lg bg-card text-card-foreground shadow-sm p-3">
            <DataTable
                columns={columns}
                data={transformedCrewList}
                pageSize={20}
            />
        </div>
    )
}
