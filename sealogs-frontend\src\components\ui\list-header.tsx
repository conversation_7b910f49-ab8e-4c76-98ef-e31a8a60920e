'use client'

import React from 'react'
import { H1 } from '@/components/ui/typography'

interface ListHeaderProps {
    /** The icon component to display */
    icon: React.ReactNode
    /** The title text to display */
    title: string
    /** Optional action component (e.g., filter actions) */
    actions?: React.ReactNode
    /** Additional CSS classes for the icon container */
    iconClassName?: string
    /** Additional CSS classes for the title */
    titleClassName?: string
}

/**
 * Reusable header component for list pages with consistent styling
 * Used across crew, vessels, maintenance, and other list components
 */
export function ListHeader({
    icon,
    title,
    actions,
    iconClassName = '',
    titleClassName = 'pl-4',
}: ListHeaderProps) {
    return (
        <div className="bg-background phablet:bg-muted pb-[7px] z-50 sticky gap-4 pt-3 inset-0 flex items-start justify-between flex-nowrap">
            <div className="flex py-3 items-baseline">
                <div className={iconClassName}>
                    {icon}
                </div>
                <H1 className={titleClassName}>{title}</H1>
            </div>
            {actions && <div>{actions}</div>}
        </div>
    )
}
