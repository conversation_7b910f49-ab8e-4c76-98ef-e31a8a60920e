'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import {
    GET_CREW_BY_IDS,
    GET_MAINTENANCE_CHECK_LIST,
} from '@/app/lib/graphQL/query'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { List } from '@/components/skeletons'
import Link from 'next/link'

import { getVesselList } from '@/app/lib/actions'
import dayjs from 'dayjs'
import Filter from '@/components/filter'
import { isEmpty, trim } from 'lodash'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { sortMaintenanceChecks } from '@/app/helpers/maintenanceHelper'
import ExportButton from '../reporting/export-button'
import { dueStatusLabel } from '../reporting/maintenance-status-activity-report'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { DataTable } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { Label } from '@/components/ui/label'
import TableWrapper from '@/components/ui/table-wrapper'
import { Card, H1 } from '@/components/ui'
import { MaintenanceFilterActions } from '@/components/filter/components/maintenance-actions'
import { ListHeader } from '@/components/ui/list-header'

export default function TaskList() {
    const [maintenanceChecks, setMaintenanceChecks] = useState<any>()
    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] =
        useState<any>()
    const [vessels, setVessels] = useState<any>()
    const [crewInfo, setCrewInfo] = useState<any>()
    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const [maintenanceChecksArray, setMaintenanceChecksArray] = useState<any>()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        if (permissions) {
            init_permissions()
        }
    }, [permissions])

    const [queryMaintenanceChecks] = useLazyQuery(GET_MAINTENANCE_CHECK_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readComponentMaintenanceCheckList[0].list
            if (data) {
                handleSetMaintenanceChecks(data)
            }
        },
        onError: (error: any) => {
            console.error('queryMaintenanceChecks error', error)
        },
    })
    useEffect(() => {
        if (isLoading) {
            loadMaintenanceChecks()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadMaintenanceChecks = async () => {
        await queryMaintenanceChecks({
            variables: {
                inventoryID: 0,
                vesselID: 0,
            },
        })
    }
    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const appendedData = activeVessels.map((item: any) => ({
            ...item,
        }))
        appendedData.push({ title: 'Other', id: 0 })
        setVessels(appendedData)
    }

    getVesselList(handleSetVessels)

    const handleSetMaintenanceChecks = (tasks: any) => {
        setMaintenanceChecks(tasks)
        setFilteredMaintenanceChecks(tasks)
        const appendedData: number[] = Array.from(
            new Set(
                tasks
                    .filter((item: any) => item.assignedTo.id > 0)
                    .map((item: any) => item.assignedTo.id),
            ),
        )
        loadCrewMemberInfo(appendedData)
    }

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setCrewInfo(data)
            }
        },
        onError: (error) => {
            console.error('queryCrewMemberInfo error', error)
        },
    })
    const loadCrewMemberInfo = async (crewId: any) => {
        await queryCrewMemberInfo({
            variables: {
                crewMemberIDs: crewId.length > 0 ? crewId : [0],
            },
        })
    }
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        var filteredTasks = maintenanceChecks

        // Vessel filter
        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.basicComponentID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.basicComponentID = { eq: +data.value }
            } else {
                delete searchFilter.basicComponentID
            }
        }

        // Status filter
        if (type === 'status') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.status = { in: data.map((item) => item.value) }
            } else if (data && !Array.isArray(data)) {
                searchFilter.status = { eq: data.value }
            } else {
                delete searchFilter.status
            }
        }

        // Assigned member filter
        if (type === 'member') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.assignedToID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.assignedToID = { eq: +data.value }
            } else {
                delete searchFilter.assignedToID
            }
        }

        // Date range
        if (type === 'dateRange') {
            if (data?.startDate && data?.endDate) {
                searchFilter.expires = {
                    gte: data.startDate,
                    lte: data.endDate,
                }
            } else {
                delete searchFilter.expires
            }
        }

        // Category
        if (type === 'category') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.maintenanceCategoryID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.maintenanceCategoryID = { eq: +data.value }
            } else {
                delete searchFilter.maintenanceCategoryID
            }
        }

        // Keyword filter
        let keyFilter = keywordFilter
        if (type === 'keyword' || keyFilter?.length > 0) {
            const keyword = data?.value?.trim().toLowerCase()
            if (keyword && keyword.length > 0) {
                filteredTasks = filteredTasks.filter((maintenanceCheck: any) =>
                    [
                        maintenanceCheck.name,
                        maintenanceCheck.comments,
                        maintenanceCheck.workOrderNumber,
                    ].some((field) => field?.toLowerCase().includes(keyword)),
                )
                keyFilter = data.value
            } else {
                keyFilter = null
            }
        }

        // Filtering based on current searchFilter

        // Filter by vessel (basicComponentID)
        if (searchFilter.basicComponentID) {
            const ids = searchFilter.basicComponentID.in || [
                searchFilter.basicComponentID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: any) =>
                ids.includes(mc.basicComponent?.id),
            )
        }

        // Filter by status
        if (searchFilter.status) {
            const statuses = searchFilter.status.in || [searchFilter.status.eq]
            filteredTasks = filteredTasks.filter((mc: any) =>
                statuses.includes(mc.status),
            )
        }

        // Filter by assignedToID
        if (searchFilter.assignedToID) {
            const ids = searchFilter.assignedToID.in || [
                searchFilter.assignedToID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: any) =>
                ids.includes(mc.assignedTo?.id),
            )
        }

        // Filter by category
        if (searchFilter.maintenanceCategoryID) {
            const ids = searchFilter.maintenanceCategoryID.in || [
                searchFilter.maintenanceCategoryID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: any) =>
                ids.includes(mc.maintenanceCategoryID),
            )
        }

        // Filter by date range
        if (searchFilter.expires) {
            filteredTasks = filteredTasks.filter(
                (mc: any) =>
                    dayjs(mc.startDate).isAfter(
                        dayjs(searchFilter.expires.gte),
                    ) &&
                    dayjs(mc.startDate).isBefore(
                        dayjs(searchFilter.expires.lte),
                    ),
            )
        }

        // Set updated filters
        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
        setFilteredMaintenanceChecks(filteredTasks)
        // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);
    }

    const downloadCsv = () => {
        if ((maintenanceChecks && vessels) === false) {
            return
        }

        const csvEntries: string[][] = [
            ['task', 'location', 'assigned to', 'due'],
        ]

        maintenanceChecks
            .filter(
                (maintenanceCheck: any) =>
                    maintenanceCheck.status !== 'Save_As_Draft',
            )
            .forEach((maintenanceCheck: any) => {
                csvEntries.push([
                    maintenanceCheck.name,
                    vessels
                        ?.filter(
                            (vessel: any) =>
                                vessel?.id == maintenanceCheck.basicComponentID,
                        )
                        .map((vessel: any) => vessel.title)
                        .join(', '),
                    crewInfo
                        .filter(
                            (crew: any) =>
                                crew.id === maintenanceCheck.assignedToID,
                        )
                        .map((crew: any, index: number) => {
                            return `${crew.firstName} ${crew.surname}`
                        })
                        .join(', '),
                    dueStatusLabel(maintenanceCheck.isOverDue),
                ])
            })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if ((maintenanceChecks && vessels) === false) {
            return
        }

        const headers: any = [['Task Name', 'Location', 'Assigned To', 'Due']]

        const body: any = maintenanceChecks
            .filter(
                (maintenanceCheck: any) =>
                    maintenanceCheck.status !== 'Save_As_Draft',
            )
            .map((maintenanceCheck: any) => {
                return [
                    maintenanceCheck.name,
                    vessels
                        ?.filter(
                            (vessel: any) =>
                                vessel?.id == maintenanceCheck.basicComponentID,
                        )
                        .map((vessel: any) => vessel.title)
                        .join(', '),
                    crewInfo
                        .filter(
                            (crew: any) =>
                                crew.id === maintenanceCheck.assignedToID,
                        )
                        .map((crew: any, index: number) => {
                            return `${crew.firstName} ${crew.surname}`
                        })
                        .join(', '),
                    dueStatusLabel(maintenanceCheck.isOverDue),
                ]
            })

        exportPdfTable({
            headers,
            body,
        })
    }

    const columns = [
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Title" />
            ),
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck: any = row.original
                return (
                    <div className="p-2 items-center text-left border-y md:border-0">
                        <div className="flex flex-col sm:flex-row">
                            <Link
                                href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                className="focus:outline-none imtems-center flex gap-1">
                                <div className="inline-block md:hidden">
                                    {(maintenanceCheck.severity === 'High' ||
                                        maintenanceCheck.severity ===
                                            'Medium') && (
                                        <ExclamationTriangleIcon
                                            className={`h-6 w-6  ${maintenanceCheck.severity === 'High' ? 'text-destructive' : 'text-slorange-1000'}`}
                                        />
                                    )}
                                </div>
                                {maintenanceCheck.name ??
                                    'Task #' +
                                        maintenanceCheck.id +
                                        ' (No Name) - ' +
                                        dayjs(maintenanceCheck.created).format(
                                            'DD/MM/YYYY',
                                        )}
                            </Link>
                        </div>
                        <div className="flex lg:hidden my-3">
                            {maintenanceCheck.basicComponentID !== null &&
                                vessels
                                    ?.filter(
                                        (vessel: any) =>
                                            vessel?.id ==
                                            maintenanceCheck.basicComponentID,
                                    )
                                    .map((vessel: any) => (
                                        <div key={vessel.id}>
                                            <Link
                                                href={`/vessel/info?id=${vessel.id}`}
                                                className="border rounded-md p-2 focus:outline-none ">
                                                {vessel.title}
                                            </Link>
                                        </div>
                                    ))}
                        </div>
                        <div className="flex flex-row gap-2 md:hidden my-3 items-center">
                            <Label className={` text-nowrap !w-20`}>
                                Assigned to
                            </Label>
                            {crewInfo &&
                                crewInfo
                                    .filter(
                                        (crew: any) =>
                                            crew.id ===
                                            maintenanceCheck.assignedToID,
                                    )
                                    .map((crew: any, index: number) => {
                                        return (
                                            <Link
                                                key={index}
                                                href={`/crew/info?id=${crew.id}`}>
                                                {crew.firstName}
                                                <span className="hidden md:inline-block">
                                                    &nbsp;{crew.surname}
                                                </span>
                                            </Link>
                                        )
                                    })}
                            <div
                                className={`inline-block border rounded-lg p-2
                                    ${maintenanceCheck?.isOverDue?.status === 'High' ? 'alert' : ''}
                                    ${maintenanceCheck?.isOverDue?.status === 'Low' || maintenanceCheck?.isOverDue?.status === 'Upcoming' || maintenanceCheck?.isOverDue?.status === 'Completed' ? 'success' : ''}
                                    ${maintenanceCheck?.isOverDue?.status === 'Medium' || maintenanceCheck?.isOverDue?.days === 'Save As Draft' ? 'text-orange-600 bg-orange-100 border-orange-600' : ''} `}>
                                <span>
                                    {maintenanceCheck?.isOverDue?.status &&
                                        ['High', 'Medium', 'Low'].includes(
                                            maintenanceCheck.isOverDue.status,
                                        ) &&
                                        maintenanceCheck?.isOverDue?.days}
                                    {maintenanceCheck?.isOverDue?.status ===
                                        'Completed' &&
                                        maintenanceCheck?.isOverDue?.days ===
                                            'Save As Draft' &&
                                        maintenanceCheck?.isOverDue?.days}
                                    {maintenanceCheck?.isOverDue?.status ===
                                        'Upcoming' &&
                                        maintenanceCheck?.isOverDue?.days}
                                    {maintenanceCheck?.isOverDue?.status ===
                                        'Completed' &&
                                        isEmpty(
                                            maintenanceCheck?.isOverDue?.days,
                                        ) &&
                                        maintenanceCheck?.isOverDue?.status}
                                    {maintenanceCheck?.isOverDue?.status ===
                                        'Completed' &&
                                        !isEmpty(
                                            maintenanceCheck?.isOverDue?.days,
                                        ) &&
                                        maintenanceCheck?.isOverDue?.days !==
                                            'Save As Draft' &&
                                        maintenanceCheck?.isOverDue?.days}
                                    {/*{maintenanceCheck.status.replaceAll('_', ' ')}*/}
                                </span>
                            </div>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'location',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Location" />
            ),
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden lg:table-cell p-2 text-left items-center ">
                        <div>
                            <Link
                                href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                className="focus:outline-none">
                                {maintenanceCheck.basicComponent.title}
                            </Link>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.basicComponent?.title || ''
                const valueB = rowB?.original?.basicComponent?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'assigned',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Assigned to" />
            ),
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:table-cell p-2 text-left items-center ">
                        <Link
                            href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                            className="">
                            {maintenanceCheck.assignedTo.name}
                        </Link>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.assignedTo?.name || ''
                const valueB = rowB?.original?.assignedTo?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'inventory ',
            header: 'Inventory  item',
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:table-cell p-2 text-left items-center ">
                        <Link
                            href={`/inventory/view?id=${maintenanceCheck.inventory.id}`}
                            className="">
                            {maintenanceCheck.inventory.item}
                        </Link>
                    </div>
                )
            },
        },
        {
            accessorKey: 'taskStatus',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Task Status" />
            ),
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:table-cell p-2 text-left items-center">
                        <div
                            className={`inline-block border rounded-lg p-2
                                    ${maintenanceCheck?.isOverDue?.status === 'High' ? 'alert' : ''}
                                    ${maintenanceCheck?.isOverDue?.status === 'Low' || maintenanceCheck?.isOverDue?.status === 'Upcoming' || maintenanceCheck?.isOverDue?.status === 'Completed' ? 'success' : ''}
                                    ${maintenanceCheck?.isOverDue?.status === 'Medium' || maintenanceCheck?.isOverDue?.days === 'Save As Draft' ? 'text-orange-1000 bg-orange-100' : ''} `}>
                            <span>
                                {maintenanceCheck?.isOverDue?.status &&
                                    ['High', 'Medium', 'Low'].includes(
                                        maintenanceCheck.isOverDue.status,
                                    ) &&
                                    maintenanceCheck?.isOverDue?.days}
                                {maintenanceCheck?.isOverDue?.status ===
                                    'Completed' &&
                                    maintenanceCheck?.isOverDue?.days ===
                                        'Save As Draft' &&
                                    maintenanceCheck?.isOverDue?.days}
                                {maintenanceCheck?.isOverDue?.status ===
                                    'Upcoming' &&
                                    maintenanceCheck?.isOverDue?.days}
                                {maintenanceCheck?.isOverDue?.status ===
                                    'Completed' &&
                                    isEmpty(
                                        maintenanceCheck?.isOverDue?.days,
                                    ) &&
                                    maintenanceCheck?.isOverDue?.status}
                                {maintenanceCheck?.isOverDue?.status ===
                                    'Completed' &&
                                    !isEmpty(
                                        maintenanceCheck?.isOverDue?.days,
                                    ) &&
                                    maintenanceCheck?.isOverDue?.days !==
                                        'Save As Draft' &&
                                    maintenanceCheck?.isOverDue?.days}
                                {/*{maintenanceCheck.status.replaceAll('_', ' ')}*/}
                            </span>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.isOverDue?.days || ''
                const valueB = rowB?.original?.isOverDue?.days || ''
                return valueA.localeCompare(valueB)
            },
        },
    ]

    return (
        <>
            <ListHeader
                title="Maintenance"
                icon={<></>}
                actions={<MaintenanceFilterActions />}
                titleClassName=""
            />
            <div className="mt-16">
                <Card>
                    {maintenanceChecks && vessels ? (
                        <DataTable
                            columns={columns}
                            data={filteredMaintenanceChecks}
                            pageSize={20}
                            onChange={handleFilterOnChange}
                        />
                    ) : (
                        <List />
                    )}
                </Card>
            </div>
        </>
    )
}

export const Table = ({
    maintenanceChecks,
    vessels,
    crewInfo,
    showVessel = false,
}: any) => {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    var maintenanceChecksArray = maintenanceChecks
        .filter(
            (maintenanceCheck: any) =>
                maintenanceCheck.status !== 'Save_As_Draft',
        )
        .map((maintenanceCheck: any) => {
            return {
                id: maintenanceCheck.id,
                name: maintenanceCheck.name,
                created: maintenanceCheck.created,
                basicComponentID: maintenanceCheck.basicComponentID,
                comments: maintenanceCheck.comments,
                description: maintenanceCheck.description,
                assignedToID: maintenanceCheck.assignedToID,
                expires: maintenanceCheck.expires, // the value of maintenanceCheck.expires here is already computed from upcomingScheduleDate()
                status: maintenanceCheck.status,
                startDate: maintenanceCheck.startDate,
                isOverDue: maintenanceCheck.isOverDue,
                isCompleted:
                    maintenanceCheck.status === 'Completed' ||
                    maintenanceCheck.isOverDue.status === 'Completed'
                        ? '1'
                        : '2',
                severity: maintenanceCheck.severity,
            }
        })
    // Completed: sort by "expires" from recent to oldest
    /* maintenanceChecksArray.sort((a: any, b: any) => {
        if (a.isOverDue.status === 'High' && b.isOverDue.status !== 'High') {
            return -1
        } else if (
            a.isOverDue.status !== 'High' &&
            b.isOverDue.status === 'High'
        ) {
            return 1
        } else if (
            a.isOverDue.status === 'Medium' &&
            b.isOverDue.status !== 'Medium'
        ) {
            return -1
        } else if (
            a.isOverDue.status !== 'Medium' &&
            b.isOverDue.status === 'Medium'
        ) {
            return 1
        } else if (
            a.isOverDue.status === 'Medium' &&
            b.isOverDue.status === 'Medium'
        ) {
            return dayjs(a.expires).diff(b.expires)
        } else if (
            a.isOverDue.status === 'High' &&
            b.isOverDue.status === 'High'
        ) {
            return dayjs(a.expires).diff(b.expires)
        } else {
            // rest of the sort logic remains the same
            if (a.isCompleted === '1' && b.isCompleted === '1') {
                if (a.expires === 'NA' && b.expires !== 'NA') {
                    return 1
                } else if (a.expires !== 'NA' && b.expires === 'NA') {
                    return -1
                } else {
                    return (
                        new Date(b.expires).getTime() -
                        new Date(a.expires).getTime()
                    )
                }
            } else if (a.isCompleted === '1') {
                return 1
            } else if (b.isCompleted === '1') {
                return -1
            } else {
                return dayjs(a.expires).diff(b.expires)
            }
        }
    }) */

    maintenanceChecksArray = sortMaintenanceChecks(maintenanceChecksArray)
    return (
        <div className="overflow-x-auto w-full block ">
            <TableWrapper headings={[]}>
                <tr className="hidden md:table-row">
                    <td className="text-left p-3"></td>
                    <td className="text-left p-3"></td>
                    {!showVessel && (
                        <td className="hidden lg:table-cell text-left p-3 border-b">
                            <Label className={` !w-full`}>Location</Label>
                        </td>
                    )}
                    <td className="hidden md:table-cell text-left p-3 border-b">
                        <Label className={` text-nowrap !w-full`}>
                            Assigned to
                        </Label>
                    </td>
                    <td className="hidden md:table-cell text-left p-3 border-b">
                        <Label className={` !w-full`}>Due</Label>
                    </td>
                </tr>
                {maintenanceChecksArray.map((maintenanceCheck: any) => (
                    <tr key={maintenanceCheck.id} className={`border-b`}>
                        <td className="hidden md:table-cell pl-2 pr-0 items-center">
                            {(maintenanceCheck.severity === 'High' ||
                                maintenanceCheck.severity === 'Medium') && (
                                <ExclamationTriangleIcon
                                    className={`h-6 w-6  ${maintenanceCheck.severity === 'High' ? 'text-destructive' : 'text-slorange-1000'}`}
                                />
                            )}
                        </td>
                        <td className="p-2 items-center text-left border-y md:border-0">
                            <div className="flex flex-col sm:flex-row">
                                <Link
                                    href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                    className="focus:outline-none imtems-center flex gap-1">
                                    <div className="inline-block md:hidden">
                                        {(maintenanceCheck.severity ===
                                            'High' ||
                                            maintenanceCheck.severity ===
                                                'Medium') && (
                                            <ExclamationTriangleIcon
                                                className={`h-6 w-6  ${maintenanceCheck.severity === 'High' ? 'text-destructive' : 'text-slorange-1000'}`}
                                            />
                                        )}
                                    </div>
                                    {maintenanceCheck.name ??
                                        'Task #' +
                                            maintenanceCheck.id +
                                            ' (No Name) - ' +
                                            dayjs(
                                                maintenanceCheck.created,
                                            ).format('DD/MM/YYYY')}
                                </Link>
                            </div>
                            {!showVessel && (
                                <div className="flex lg:hidden my-3">
                                    {maintenanceCheck.basicComponent?.id >
                                        0 && (
                                        <div>
                                            <Link
                                                href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                                className="group-hover:text-sllightblue-800 border border-slblue-100 rounded-md p-2 bg-sllightblue-50 focus:outline-none ">
                                                {
                                                    maintenanceCheck
                                                        .basicComponent.title
                                                }
                                            </Link>
                                        </div>
                                    )}
                                </div>
                            )}
                            <div className="flex flex-row gap-2 md:hidden my-3 items-center">
                                <Label className={` text-nowrap !w-20`}>
                                    Assigned to
                                </Label>
                                {crewInfo &&
                                    crewInfo
                                        .filter(
                                            (crew: any) =>
                                                crew.id ===
                                                maintenanceCheck.assignedToID,
                                        )
                                        .map((crew: any, index: number) => {
                                            return (
                                                <Link
                                                    key={index}
                                                    href={`/crew/info?id=${crew.id}`}
                                                    className="rounded-lg p-2 outline-none ">
                                                    {crew.firstName}
                                                    <span className="hidden md:inline-block">
                                                        &nbsp;{crew.surname}
                                                    </span>
                                                </Link>
                                            )
                                        })}
                                <div
                                    className={`inline-block border rounded-lg p-2
                                        ${maintenanceCheck?.isOverDue?.status === 'High' ? 'alert' : ''}
                                        ${maintenanceCheck?.isOverDue?.status === 'Low' || maintenanceCheck?.isOverDue?.status === 'Upcoming' || maintenanceCheck?.isOverDue?.status === 'Completed' ? 'success' : ''}
                                        ${maintenanceCheck?.isOverDue?.status === 'Medium' || maintenanceCheck?.isOverDue?.days === 'Save As Draft' ? 'text-orange-600 bg-orange-100 border-orange-600' : ''} `}>
                                    <span>
                                        {maintenanceCheck?.isOverDue?.status &&
                                            ['High', 'Medium', 'Low'].includes(
                                                maintenanceCheck.isOverDue
                                                    .status,
                                            ) &&
                                            maintenanceCheck?.isOverDue?.days}
                                        {maintenanceCheck?.isOverDue?.status ===
                                            'Completed' &&
                                            maintenanceCheck?.isOverDue
                                                ?.days === 'Save As Draft' &&
                                            maintenanceCheck?.isOverDue?.days}
                                        {maintenanceCheck?.isOverDue?.status ===
                                            'Upcoming' &&
                                            maintenanceCheck?.isOverDue?.days}
                                        {maintenanceCheck?.isOverDue?.status ===
                                            'Completed' &&
                                            isEmpty(
                                                maintenanceCheck?.isOverDue
                                                    ?.days,
                                            ) &&
                                            maintenanceCheck?.isOverDue?.status}
                                        {maintenanceCheck?.isOverDue?.status ===
                                            'Completed' &&
                                            !isEmpty(
                                                maintenanceCheck?.isOverDue
                                                    ?.days,
                                            ) &&
                                            maintenanceCheck?.isOverDue
                                                ?.days !== 'Save As Draft' &&
                                            maintenanceCheck?.isOverDue?.days}
                                        {/*{maintenanceCheck.status.replaceAll('_', ' ')}*/}
                                    </span>
                                </div>
                            </div>
                        </td>
                        {!showVessel && (
                            <td className="hidden lg:table-cell p-2 text-left items-center ">
                                {maintenanceCheck.basicComponent?.id > 0 && (
                                    <div>
                                        <Link
                                            href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                            className="group-hover:text-sllightblue-800 focus:outline-none">
                                            {
                                                maintenanceCheck.basicComponent
                                                    .title
                                            }
                                        </Link>
                                    </div>
                                )}
                            </td>
                        )}
                        <td className="hidden md:table-cell p-2 text-left items-center ">
                            {maintenanceCheck.assignedTo?.id > 0 && (
                                <Link
                                    href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                                    className="bg-slblue-50 border border-slblue-200 rounded-lg p-2 outline-none ">
                                    {maintenanceCheck.assignedTo.name}
                                </Link>
                            )}
                        </td>
                        <td className="hidden md:table-cell p-2 text-left items-center">
                            {maintenanceCheck?.archived ? (
                                <div className="inline-block border rounded-lg p-2 text-xs text-slgreen-1000 bg-slneon-100 border-slgreen-1000">
                                    <span>Completed recurring</span>
                                </div>
                            ) : (
                                <div
                                    className={`inline-block border rounded-lg p-2 text-xs
                                        ${maintenanceCheck?.isOverDue?.status === 'High' ? 'text-slred-1000 bg-slred-100 border-slred-1000' : ''}
                                        ${maintenanceCheck?.isOverDue?.status === 'Low' || maintenanceCheck?.isOverDue?.status === 'Upcoming' || maintenanceCheck?.isOverDue?.status === 'Completed' ? 'text-slgreen-1000 bg-slneon-100 border-slgreen-1000' : ''}
                                        ${maintenanceCheck?.isOverDue?.status === 'Medium' || maintenanceCheck?.isOverDue?.days === 'Save As Draft' ? 'text-orange-1000 bg-orange-100' : ''} `}>
                                    <span>
                                        {maintenanceCheck?.isOverDue?.status &&
                                            ['High', 'Medium', 'Low'].includes(
                                                maintenanceCheck.isOverDue
                                                    .status,
                                            ) &&
                                            maintenanceCheck?.isOverDue?.days}
                                        {maintenanceCheck?.isOverDue?.status ===
                                            'Completed' &&
                                            maintenanceCheck?.isOverDue
                                                ?.days === 'Save As Draft' &&
                                            maintenanceCheck?.isOverDue?.days}
                                        {maintenanceCheck?.isOverDue?.status ===
                                            'Upcoming' &&
                                            maintenanceCheck?.isOverDue?.days}
                                        {maintenanceCheck?.isOverDue?.status ===
                                            'Completed' &&
                                            isEmpty(
                                                maintenanceCheck?.isOverDue
                                                    ?.days,
                                            ) &&
                                            maintenanceCheck?.isOverDue?.status}
                                        {maintenanceCheck?.isOverDue?.status ===
                                            'Completed' &&
                                            !isEmpty(
                                                maintenanceCheck?.isOverDue
                                                    ?.days,
                                            ) &&
                                            maintenanceCheck?.isOverDue
                                                ?.days !== 'Save As Draft' &&
                                            maintenanceCheck?.isOverDue?.days}
                                        {/*{maintenanceCheck.status.replaceAll('_', ' ')}*/}
                                    </span>
                                </div>
                            )}
                        </td>
                    </tr>
                ))}
            </TableWrapper>
        </div>
    )
}
