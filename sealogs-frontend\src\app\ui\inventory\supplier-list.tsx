'use client'
import React, { useEffect, useState } from 'react'
import {
    <PERSON><PERSON>,
    DialogTrigger,
    Heading,
    Popover,
    Link,
} from 'react-aria-components'
import { ChatBubbleBottomCenterTextIcon } from '@heroicons/react/24/outline'
import { List } from '@/components/skeletons'
import { getSupplier } from '@/app/lib/actions'
import Filter from '@/components/filter'
import { useLazyQuery } from '@apollo/client'
import { GET_SUPPLIER } from '@/app/lib/graphQL/query'
import { isEmpty, trim } from 'lodash'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import PopoverWrapper from '@/components/ui/popover-wrapper'

export default function SupplierList() {
    const [suppliers, setSuppliers] = useState([] as any)
    const [filter, setFilter] = useState({} as SearchFilter)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    // getSupplier(setSuppliers)

    const [isLoading, setIsLoading] = useState(true)
    const [querySupplier] = useLazyQuery(GET_SUPPLIER, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSuppliers.nodes
            if (data) {
                setSuppliers(data)
            }
        },
        onError: (error: any) => {
            console.error('querySupplier error', error)
        },
    })
    useEffect(() => {
        if (isLoading) {
            loadSupplier()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadSupplier = async (
        searchFilter: SearchFilter = {},
        searchkeywordFilter: any = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await querySupplier({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) => r.data.readSuppliers.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readSuppliers.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            setSuppliers(responses)
        } else {
            await querySupplier({
                variables: {
                    filter: searchFilter,
                },
            })
        }
    }
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        let keyFilter = keywordFilter
        if (type === 'keyword') {
            if (!isEmpty(trim(data.value))) {
                keyFilter = [
                    { name: { contains: data.value } },
                    { website: { contains: data.value } },
                    { phone: { contains: data.value } },
                    { email: { contains: data.value } },
                    { address: { contains: data.value } },
                ]
            } else {
                keyFilter = []
            }
        }

        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
        loadSupplier(searchFilter, keyFilter)
    }
    const columns: ExtendedColumnDef<any, unknown>[] = [
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Suppliers" />
            ),
            cell: ({ row }: { row: any }) => {
                const supplier: any = row.original
                return (
                    <div className="px-2 lg:px-6 py-3 whitespace-nowrap text-left">
                        <div className=" font-medium  ">
                            <Link
                                href={`/inventory/suppliers/view?id=${supplier.id}`}>
                                {supplier.name}
                            </Link>
                            {supplier.phone ? (
                                <div className="inline-block rounded px-3 py-1 ml-3   ">
                                    {supplier.phone}
                                </div>
                            ) : (
                                ''
                            )}
                        </div>
                        <div className=" tracking-[.02px]  ">
                            {supplier.email ? supplier.email : ''}
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'address',
            header: 'Address',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const supplier = row.original
                return (
                    <div className="px-2 py-3 whitespace-nowrap">
                        <div className="  ">
                            {supplier.address ? supplier.address : '-'}
                        </div>
                    </div>
                )
            },
        },
        {
            accessorKey: 'website',
            header: 'Website',
            cell: ({ row }: { row: any }) => {
                const supplier = row.original
                return (
                    <div className="px-2 py-3 whitespace-nowrap">
                        <a
                            href={`https://${supplier.website ? supplier.website : '-'}`}
                            target="_blank">
                            <div className="  ">
                                {supplier.website ? supplier.website : '-'}
                            </div>
                        </a>
                    </div>
                )
            },
        },
        {
            accessorKey: 'contactPeople',
            header: 'Contact People',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const supplier = row.original
                return (
                    <div className="px-2 py-3 whitespace-nowrap">
                        <div className="  ">
                            {supplier.phone ? supplier.phone : ''}
                        </div>
                        <div className="  ">
                            {supplier.email ? supplier.email : ''}
                        </div>
                    </div>
                )
            },
        },
        {
            accessorKey: 'notes',
            header: 'Notes',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                return (
                    <div className="px-2 py-3 whitespace-nowrap">
                        <div className="  ">
                            <DialogTrigger>
                                <Button className=" ml-2 outline-none">
                                    <ChatBubbleBottomCenterTextIcon className="w-5 h-5   " />
                                </Button>
                                <Popover>
                                    <PopoverWrapper>
                                        Notes placeholder
                                    </PopoverWrapper>
                                </Popover>
                            </DialogTrigger>
                        </div>
                    </div>
                )
            },
        },
    ]
    return (
        <>
            {suppliers ? (
                <DataTable
                    columns={columns}
                    data={suppliers}
                    pageSize={20}
                    onChange={handleFilterOnChange}
                />
            ) : (
                <List />
            )}
        </>
    )
}
